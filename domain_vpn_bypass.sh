#!/system/bin/sh

echo "设置基于域名的VPN分流..."

# 直连域名列表（类似Clash的DIRECT规则）
DIRECT_DOMAINS="
cocos.com
alipay.com
95516.com
unionpay.com
icbc.com.cn
ccb.com
qq.com
weixin.qq.com
wechat.com
baidu.com
taobao.com
tmall.com
"

# VPN域名列表（走游戏加速器）
VPN_DOMAINS="
ai.microsoft.com
openai.com
github.com
google.com
youtube.com
facebook.com
twitter.com
"

# 创建自定义DNS解析
setup_dns_routing() {
    echo "设置DNS分流规则..."
    
    # 创建直连IP集合
    ipset create direct_ips hash:ip 2>/dev/null
    
    # 为直连域名解析IP并添加到ipset
    for domain in $DIRECT_DOMAINS; do
        echo "处理直连域名: $domain"
        # 解析域名获取IP
        ips=$(nslookup $domain ******* 2>/dev/null | grep "Address:" | grep -v "#53" | awk '{print $2}')
        for ip in $ips; do
            if [ -n "$ip" ] && [ "$ip" != "" ]; then
                ipset add direct_ips $ip 2>/dev/null
                echo "  添加直连IP: $ip"
            fi
        done
    done
    
    # 为直连IP设置路由规则
    iptables -t mangle -A OUTPUT -m set --match-set direct_ips dst -j MARK --set-mark 0x1
    ip rule add fwmark 0x1 lookup main pref 9000
    
    echo "✓ 直连域名规则设置完成"
}

# 设置基于IP的分流
setup_ip_routing() {
    echo "设置IP分流规则..."
    
    # 国内IP段直连
    CHINA_IPS="
    *******/24
    *******/23
    *******/21
    ********/19
    *******/24
    *******/24
    *******/23
    *******/22
    *******/24
    *******/24
    "
    
    # 为国内IP添加直连规则
    for ip_range in $CHINA_IPS; do
        ip rule add from all to $ip_range lookup main pref 9100
        echo "  国内IP直连: $ip_range"
    done
    
    # 本地网络直连
    ip rule add from all to ***********/16 lookup main pref 9200
    ip rule add from all to 10.0.0.0/8 lookup main pref 9201
    ip rule add from all to **********/12 lookup main pref 9202
    ip rule add from all to *********/8 lookup main pref 9203
    
    echo "✓ IP分流规则设置完成"
}

# 设置应用级分流（保留原有功能）
setup_app_routing() {
    echo "设置应用分流规则..."
    
    # 获取应用UID函数
    get_app_uid() {
        dumpsys package "$1" 2>/dev/null | grep "userId=" | head -1 | cut -d'=' -f2
    }
    
    # 直连应用列表
    DIRECT_APPS="com.tencent.mm com.eg.android.AlipayGphone"
    
    for app in $DIRECT_APPS; do
        uid=$(get_app_uid "$app")
        if [ -n "$uid" ] && [ "$uid" != "" ]; then
            ip rule add from all uidrange "$uid-$uid" lookup main pref 9300
            echo "  应用直连: $app (UID: $uid)"
        fi
    done
    
    echo "✓ 应用分流规则设置完成"
}

# 设置全局VPN规则（最低优先级）
setup_global_vpn() {
    echo "设置全局VPN规则..."
    
    # 你的原始VPN规则（优先级最低）
    ip rule add from all fwmark 0x0/0x20000 uidrange 0-99999 lookup tun0 pref 11600
    ip route add 0.0.0.0/0 dev tun0 table tun0
    
    echo "✓ 全局VPN规则设置完成"
}

# 清理旧规则
cleanup_rules() {
    echo "清理旧规则..."
    
    # 清理iptables规则
    iptables -t mangle -F OUTPUT 2>/dev/null
    
    # 清理ipset
    ipset destroy direct_ips 2>/dev/null
    
    # 清理路由规则
    ip rule show | grep -E "pref (9000|9100|9200|9201|9202|9203|9300)" | while read line; do
        pref=$(echo "$line" | grep -o "pref [0-9]*" | cut -d' ' -f2)
        [ -n "$pref" ] && ip rule del pref "$pref" 2>/dev/null
    done
    
    echo "✓ 清理完成"
}

# 显示当前规则
show_rules() {
    echo "当前分流规则："
    echo "=== 路由规则 ==="
    ip rule show | head -20
    echo ""
    echo "=== 直连IP集合 ==="
    ipset list direct_ips 2>/dev/null | head -10
    echo ""
    echo "=== iptables标记规则 ==="
    iptables -t mangle -L OUTPUT -n | head -10
}

# 主程序
case "$1" in
    "setup")
        cleanup_rules
        setup_dns_routing
        setup_ip_routing
        setup_app_routing
        setup_global_vpn
        echo "✅ 域名分流设置完成！"
        ;;
    "show")
        show_rules
        ;;
    "clean")
        cleanup_rules
        ;;
    *)
        echo "用法: $0 {setup|show|clean}"
        echo "  setup - 设置域名分流规则"
        echo "  show  - 显示当前规则"
        echo "  clean - 清理所有规则"
        echo ""
        echo "直接运行默认执行setup..."
        cleanup_rules
        setup_dns_routing
        setup_ip_routing
        setup_app_routing
        setup_global_vpn
        echo "✅ 域名分流设置完成！"
        ;;
esac
