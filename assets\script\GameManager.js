cc.Class({
    extends: cc.Component,

    properties: {
        // 玩家节点
        player: cc.Node,

        // 平台管理器
        platformManager: cc.Node,

        // 金币管理器
        coinManager: cc.Node,

        // 分数管理器
        scoreManager: cc.Node,

        // 背景管理器
        backgroundManager: cc.Node,

        // 暂停按钮
        pauseButton: cc.Node,

        // 暂停页面
        pauseAdvert: cc.Node,

        // 继续游戏按钮
        resumeButton: cc.Node,

        curScore:cc.Node,

        maxScore:cc.Node,

        gameSettlement:cc.Node,


        resurgencebutton:cc.Node,

        gostartbutton:cc.Node,


        // 初始所有东西向左移动速度
        startSpeed: 200,

        // 速度增加率（每秒）
        speedIncreaseRate: 10,

        // 最大速度
        maxSpeed: 500
    },

    onLoad() {
        // 初始化物理引擎
        let physicsManager = cc.director.getPhysicsManager();
        physicsManager.enabled = true;
        physicsManager.gravity = cc.v2(0, -800); // 设置重力
        
        // 初始化游戏状态
        this.isGameOver = false;
        this.isPaused = false;

         this.gameSettlement.active = false;
        
        // 初始化距离
        this.distance = 0;

        // 获取平台管理器组件
        this.platformManagerComp = this.platformManager.getComponent('PlatformManager');

        // 获取金币管理器组件
        if (this.coinManager) {
            this.coinManagerComp = this.coinManager.getComponent('CoinMgr');
        }

        // 获取玩家组件
        if (this.player) {
            this.playerComp = this.player.getComponent('player');
        }

        // 获取分数管理器组件
        if (this.scoreManager) {
            this.scoreManagerComp = this.scoreManager.getComponent('ScoreManager');
        }

        // 获取背景管理器组件
        if (this.backgroundManager) {
            this.backgroundManagerComp = this.backgroundManager.getComponent('BackgroundManager');
        }

        // 初始化暂停功能
        this.initPauseSystem();



        //重开游戏
        this.resurgencebutton.on('click', this.restartGame, this);

        this.gostartbutton.on('click', this.gostartGame, this);

   


    },

    gostartGame(){
        cc.director.loadScene("start");
    },

    restartGame(){
        cc.director.loadScene("game");
       
    },

    initPauseSystem() {

        // 绑定暂停按钮点击事件 - 直接调用pauseGame函数
            this.pauseButton.on('click', this.pauseGame, this);
        // 绑定继续游戏按钮点击事件 - 直接调用resumeGame函数
            this.resumeButton.on('click', this.resumeGame, this);

    },

    start() {
        // 设置初始速度
        this.currentSpeed = this.startSpeed;
        
            this.platformManagerComp.setMoveSpeed(this.currentSpeed);
        
     
            this.coinManagerComp.setMoveSpeed(this.currentSpeed);
    
            this.backgroundManagerComp.setScrollSpeed(this.currentSpeed * 0.3); // 背景滚动速度稍慢
        
    },

    update(dt) {
        if (this.isGameOver) return;

        // 增加距离（仅用于速度计算，不影响分数）
        this.distance += this.currentSpeed * dt;

        // 逐渐增加速度
        this.currentSpeed = Math.min(this.maxSpeed, this.currentSpeed + this.speedIncreaseRate * dt);
    
            this.platformManagerComp.setMoveSpeed(this.currentSpeed);
        
       
            this.coinManagerComp.setMoveSpeed(this.currentSpeed);
     
            this.backgroundManagerComp.setScrollSpeed(this.currentSpeed * 0.3); // 背景滚动速度稍慢
        

        // 检查游戏是否结束
        this.checkGameOver();
    },
    
    
    checkGameOver() {
        // 检查玩家是否掉出了屏幕
        if (this.player && this.player.y < -cc.winSize.height / 2 - 100) {
            this.gameOver();
        }
    },
    
    gameOver() {
        if (this.isGameOver) return; // 防止多次调用

        this.isGameOver = true;

        // 通知分数管理器游戏结束
        if (this.scoreManagerComp) {
            this.scoreManagerComp.onGameOver();

        }

        // 停止平台移动
  
            this.platformManagerComp.setMoveSpeed(0);
            this.coinManagerComp.setMoveSpeed(0);
        // 停止背景滚动
       
            this.backgroundManagerComp.setScrollSpeed(0);
            
            this.gameSettlement.active = true;

        this.curScoreJs= this.curScore.getComponent(cc.Label);
        
        this.curScoreJs.string= this.scoreManagerComp.getScore();

        this.maxScoreJs=this.maxScore.getComponent(cc.Label);

        this.maxScoreJs.string = "最高分数: " + this.scoreManagerComp.getHighScore();
        
        
    },
    


    // 暂停游戏
    pauseGame() {
        if (this.isGameOver || this.isPaused) return;

        this.isPaused = true;

        // 暂停各个管理器组件
      
            this.platformManagerComp.enabled = false;
        
      
            this.coinManagerComp.enabled = false;
        

            this.backgroundManagerComp.enabled = false;
        

        // 显示暂停页面
        if (this.pauseAdvert) {
            this.pauseAdvert.active = true;
        }
    },

    // 继续游戏
    resumeGame() {
        if (this.isGameOver || !this.isPaused) return;

        this.isPaused = false;

        // 恢复各个管理器组件

            this.platformManagerComp.enabled = true;
      
            this.coinManagerComp.enabled = true;
      
            this.backgroundManagerComp.enabled = true;
        

        // 隐藏暂停页面
        if (this.pauseAdvert) {
            this.pauseAdvert.active = false;
        }
    },

    onDestroy() {
        // 移除事件监听
  
            this.pauseButton.off('click', this.pauseGame, this);
  
            this.resumeButton.off('click', this.resumeGame, this);
           
            this.resurgencebutton.off('click', this.restartGame, this);
            this.gostartbutton.off('click', this.gostartGame, this);

    }
});
