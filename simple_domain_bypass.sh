#!/system/bin/sh

echo "设置简单域名分流..."

# 直连域名的IP地址（手动解析后的结果）
# 你可以通过 nslookup 命令获取这些域名的IP
setup_direct_ips() {
    echo "设置直连IP规则..."
    
    # 支付宝相关IP
    ip rule add from all to ***********/24 lookup main pref 9000    # 支付宝
    ip rule add from all to *************/24 lookup main pref 9001  # 微信支付
    
    # 腾讯相关IP
    ip rule add from all to ************/24 lookup main pref 9010   # 微信服务器
    ip rule add from all to ***********/24 lookup main pref 9011    # QQ服务器
    
    # 银行相关IP
    ip rule add from all to ************/24 lookup main pref 9020   # 工商银行
    ip rule add from all to ***********/24 lookup main pref 9021    # 建设银行
    
    # 国内常用服务IP
    ip rule add from all to ************/24 lookup main pref 9030   # 百度
    ip rule add from all to ************/24 lookup main pref 9031   # 淘宝
    
    echo "✓ 直连IP规则设置完成"
}

# 基于域名后缀的简单分流
setup_domain_suffix_routing() {
    echo "设置域名后缀分流..."
    
    # 创建一个简单的DNS劫持方案
    # 通过修改特定域名的解析来实现分流
    
    # 国内域名后缀直连
    DIRECT_SUFFIXES=".cn .com.cn .net.cn .org.cn .gov.cn .edu.cn"
    
    # 这里我们使用IP段来模拟域名分流
    # 因为安卓上直接域名分流比较复杂
    
    # 中国大陆IP段（部分）
    CHINA_IP_RANGES="
    *******/24
    *******/23
    *******/21
    ********/19
    ********/8
    ********/8
    ********/8
    ********/8
    ********/8
    ********/8
    ********/8
    ********/8
    60.0.0.0/8
    ********/8
    *********/8
    *********/8
    *********/8
    110.0.0.0/8
    *********/8
    *********/8
    *********/8
    1********/8
    *********/8
    *********/8
    *********/8
    *********/8
    *********/8
    120.0.0.0/8
    *********/8
    *********/8
    *********/8
    *********/8
    *********/8
    "
    
    # 为中国IP段添加直连规则
    pref=9100
    for ip_range in $CHINA_IP_RANGES; do
        ip rule add from all to $ip_range lookup main pref $pref
        pref=$((pref + 1))
        if [ $pref -gt 9200 ]; then
            break  # 避免规则过多
        fi
    done
    
    echo "✓ 域名后缀分流设置完成"
}

# 设置本地网络直连
setup_local_network() {
    echo "设置本地网络直连..."
    
    # 本地网络段
    ip rule add from all to ***********/16 lookup main pref 9300
    ip rule add from all to 10.0.0.0/8 lookup main pref 9301
    ip rule add from all to **********/12 lookup main pref 9302
    ip rule add from all to 1********/8 lookup main pref 9303
    
    # DNS服务器直连
    ip rule add from all to *******/32 lookup main pref 9310
    ip rule add from all to *******/32 lookup main pref 9311
    ip rule add from all to ***************/32 lookup main pref 9312
    
    echo "✓ 本地网络直连设置完成"
}

# 清理规则
cleanup_rules() {
    echo "清理旧规则..."
    
    # 删除9000-9999优先级的规则
    ip rule show | grep -E "pref 9[0-9]{3}" | while read line; do
        pref=$(echo "$line" | grep -o "pref [0-9]*" | cut -d' ' -f2)
        [ -n "$pref" ] && ip rule del pref "$pref" 2>/dev/null
    done
    
    echo "✓ 清理完成"
}

# 显示规则
show_rules() {
    echo "当前分流规则："
    ip rule show | grep -E "pref 9[0-9]{3}" | head -20
    echo ""
    echo "规则说明："
    echo "  9000-9099: 特定服务IP直连"
    echo "  9100-9299: 中国IP段直连"
    echo "  9300-9399: 本地网络直连"
    echo "  11600:     其他流量走VPN"
}

# 主程序
case "$1" in
    "setup")
        cleanup_rules
        setup_direct_ips
        setup_domain_suffix_routing
        setup_local_network
        
        # 设置全局VPN规则（最低优先级）
        echo "设置全局VPN规则..."
        ip rule add from all fwmark 0x0/0x20000 uidrange 0-99999 lookup tun0 pref 11600
        ip route add 0.0.0.0/0 dev tun0 table tun0
        
        echo "✅ 域名分流设置完成！"
        echo ""
        echo "分流逻辑："
        echo "  ✓ 国内网站/服务 → 直连"
        echo "  ✓ 本地网络 → 直连"
        echo "  ✓ 其他网站 → VPN加速"
        ;;
    "show")
        show_rules
        ;;
    "clean")
        cleanup_rules
        ;;
    *)
        echo "用法: $0 {setup|show|clean}"
        echo "  setup - 设置域名分流"
        echo "  show  - 显示规则"
        echo "  clean - 清理规则"
        echo ""
        echo "直接运行默认执行setup..."
        cleanup_rules
        setup_direct_ips
        setup_domain_suffix_routing
        setup_local_network
        
        echo "设置全局VPN规则..."
        ip rule add from all fwmark 0x0/0x20000 uidrange 0-99999 lookup tun0 pref 11600
        ip route add 0.0.0.0/0 dev tun0 table tun0
        
        echo "✅ 域名分流设置完成！"
        ;;
esac
