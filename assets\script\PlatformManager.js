cc.Class({
    extends: cc.Component,

    properties: {
        moveSpeed: {
            default: 200,
            tooltip: "地面移动速度"
        },
        minGapX: {
            default: 50,
            tooltip: "地面之间的最小间距"
        },
        maxGapX: {
            default: 200,
            tooltip: "地面之间的最大间距"
        },
        platformPrefabs: {
            default: [],
            type: [cc.Prefab],
            tooltip: "地面预制体数组"
        },
        preGenerateDistance: {
            default: 2.5,
            tooltip: "提前多少屏幕宽度生成地面"
        }
    },

    onLoad() {
        // 获取屏幕宽度，用于计算生成边界
        this.screenWidth = cc.winSize.width;

        // 初始化状态变量
        this.lastGenerateTime = 0;       // 上次生成时间，防止过度生成

        // 统一的地面管理数组
        this.allPlatforms = [];          // 所有地面（包括初始地面和动态生成的）
        this.platformPool = [];          // 地面对象池，用于回收重用

        // 创建初始地面
        this.createInitialPlatform();
    },

    // 创建初始地面作为起点
    createInitialPlatform() {
        let initialPlatform = cc.instantiate(this.platformPrefabs[0]);
        initialPlatform.parent = this.node;
        initialPlatform.x = -115;
        initialPlatform.y = -130;
        initialPlatform._isActive = true;
        initialPlatform.group = 'Ground';

        this.allPlatforms.push(initialPlatform);
       
    },

    start() {},

    update(dt) {
        // 每帧执行的主要逻辑
        this.moveAllPlatforms(dt);          // 移动所有地面
        this.checkAndGeneratePlatforms();   // 检查是否需要生成新地面
        this.recyclePlatforms();            // 回收移出屏幕的地面
    },

    // 移动所有地面
    moveAllPlatforms(dt) {
        this.allPlatforms.forEach(platform => {
            if (platform._isActive) {
                platform.x -= this.moveSpeed * dt;
            }
        });
    },

    // 检查是否需要生成新的地面
    checkAndGeneratePlatforms() {
        // 计算目标右边界：屏幕右边缘 + 预生成距离
        let targetRightEdge = this.screenWidth / 2 + this.screenWidth * this.preGenerateDistance;
        let currentRightmostX = this.getRightmostPlatformX();

        // 如果最右边的地面距离不够远，就生成新地面
        if (currentRightmostX < targetRightEdge) {
            let now = Date.now();
            // 限制生成频率和数量，避免过度生成
            if (this.allPlatforms.length < 20 && (!this.lastGenerateTime || (now - this.lastGenerateTime) > 50)) {
                this.generateNewPlatform();
                this.lastGenerateTime = now;
            }
        }
    },

    // 生成一个新的地面
    generateNewPlatform() {
        // 获取当前最右边地面的位置
        let currentRightmostX = this.getRightmostPlatformX();

        // 随机选择一个地面预制体
        let prefabToUse = this.getRandomPlatformPrefab();

        // 尝试从对象池获取地面，如果没有就创建新的
        let newPlatform = this.getPlatformFromPool();
        if (!newPlatform) {
            newPlatform = cc.instantiate(prefabToUse);
        }

        // 设置地面的父节点和位置
        newPlatform.parent = this.node;
        let gap = this.minGapX + Math.random() * (this.maxGapX - this.minGapX); // 随机间距
        let newX = currentRightmostX + gap + newPlatform.width / 2;             // X位置
        let newY = this.calculatePlatformY();                                   // Y位置

        // 应用位置和属性
        newPlatform.x = newX;
        newPlatform.y = newY;
        newPlatform._isActive = true;         // 标记为活跃状态
        newPlatform.group = 'Ground';         // 设置碰撞分组

        // 添加到统一的地面数组
        this.allPlatforms.push(newPlatform);
    },

    // 从地面预制体数组中随机选择一个
    getRandomPlatformPrefab() {
        let randomIndex = Math.floor(Math.random() * this.platformPrefabs.length);
        return this.platformPrefabs[randomIndex];
    },

    // 计算地面的Y位置（高度）
    calculatePlatformY() {
        // 随机高度，增加游戏变化
        let heights = [-200, -150, -150, -100, -70, -40,50, 100];
        return heights[Math.floor(Math.random() * heights.length)];
    },

    // 从对象池中获取可重用的地面节点
    getPlatformFromPool() {
        if (this.platformPool.length > 0) {
            let platform = this.platformPool.pop();
            platform.active = true; // 重新激活节点
            return platform;
        }
        return null; // 对象池为空，需要创建新节点
    },

    // 回收移出屏幕的地面到对象池
    recyclePlatforms() {
        let leftBoundary = -this.screenWidth / 2 - 100; // 屏幕左边界外100像素

        // 从后往前遍历，避免删除元素时索引错乱
        for (let i = this.allPlatforms.length - 1; i >= 0; i--) {
            let platform = this.allPlatforms[i];
            // 如果地面移出了屏幕左边界
            if (platform._isActive && platform.x + platform.width / 2 < leftBoundary) {
                platform._isActive = false;    // 标记为非活跃
                platform.active = false;       // 隐藏节点
                this.platformPool.push(platform); // 放入对象池重用
                this.allPlatforms.splice(i, 1); // 从活跃数组中移除
            }
        }
    },

    // 获取当前最右边地面的X位置
    getRightmostPlatformX() {
        let rightmostX = -Infinity;

        // 检查所有活跃的地面
        this.allPlatforms.forEach(platform => {
            if (platform._isActive) {
                let rightEdge = platform.x + platform.width / 2;
                rightmostX = Math.max(rightmostX, rightEdge);
            }
        });


        return rightmostX;
    },

    // 设置地面移动速度（由GameManager调用）
    setMoveSpeed(speed) {
        this.moveSpeed = speed;
    },

    // 重置所有地面到初始状态（游戏重新开始时调用）
    resetPlatforms() {
        // 销毁所有地面
        this.allPlatforms.forEach(platform => {
            if (platform && platform.isValid) {
                platform.destroy();
            }
        });
        this.allPlatforms = [];

        // 清空对象池
        this.platformPool.forEach(platform => {
            if (platform && platform.isValid) {
                platform.destroy();
            }
        });
        this.platformPool = [];

        // 重新创建初始地面
        this.createInitialPlatform();
    },

});